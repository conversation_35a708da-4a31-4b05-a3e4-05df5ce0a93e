"""
<PERSON><PERSON><PERSON> to run the circular video overlay with specified files.
"""
from circular_video_overlay import overlay_circular_video
import os

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(__file__))

# Specify the file paths
background_path = "high-quality-roofing.png"  # Update this path if needed
overlay_path = "CJ.mp4"  # Update this path if needed
output_path = "output_with_overlay.mp4"

# Check if files exist
if not os.path.exists(background_path):
    print(f"Warning: Background file '{background_path}' not found.")
    print("Please make sure the file exists or update the path.")

if not os.path.exists(overlay_path):
    print(f"Warning: Overlay file '{overlay_path}' not found.")
    print("Please make sure the file exists or update the path.")

# Run the overlay if files exist
if os.path.exists(background_path) and os.path.exists(overlay_path):
    print(f"Processing overlay of '{overlay_path}' onto '{background_path}'...")
    overlay_circular_video(
        background_path=background_path,
        overlay_video_path=overlay_path,
        output_path=output_path,
        overlay_size=200,
        position=("right", "bottom")
    )
    print(f"Overlay completed! Output saved to: {output_path}")
else:
    print("Cannot proceed due to missing files.")
    print("Please place the files in the correct location or update the paths in this script.")
