import numpy as np
import cv2
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip
import argparse
import os

def circular_mask(frame):
    """Apply a circular mask to a frame, making everything outside the circle transparent."""
    height, width = frame.shape[:2]
    mask = np.zeros((height, width), dtype=np.uint8)
    center = (width // 2, height // 2)
    radius = min(center)  # full circle in square
    cv2.circle(mask, center, radius, 255, -1)

    # Convert to BGRA to handle transparency
    if frame.shape[2] == 3:  # RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGRA)
    # Set alpha channel based on mask
    frame[:, :, 3] = mask
    return frame

def overlay_circular_video(background_path, overlay_video_path, output_path, 
                          overlay_size=200, position=("left", "bottom")):
    """
    Overlay a circular video onto a background image or video.
    
    Parameters:
    -----------
    background_path : str
        Path to the background image or video
    overlay_video_path : str
        Path to the video to be overlaid in circular shape
    output_path : str
        Path to save the output video
    overlay_size : int
        Height of the overlay video in pixels
    position : tuple
        Position of the overlay (e.g., ("left", "bottom"), (10, 20))
    """
    # Determine if background is image or video
    bg_ext = os.path.splitext(background_path)[1].lower()
    is_image = bg_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
    
    # Load background
    if is_image:
        # Load static image and get video duration from overlay
        overlay = VideoFileClip(overlay_video_path)
        bg = ImageClip(background_path, duration=overlay.duration)
    else:
        # Both are videos
        bg = VideoFileClip(background_path)
        overlay = VideoFileClip(overlay_video_path)
        
        # If overlay is longer than background, trim it
        if overlay.duration > bg.duration:
            overlay = overlay.subclip(0, bg.duration)
    
    # Resize overlay and apply circular mask
    overlay = overlay.resize(height=overlay_size)
    overlay = overlay.fl_image(circular_mask).set_position(position)
    
    # Combine clips
    final = CompositeVideoClip([bg, overlay])
    
    # Write output file with appropriate settings
    final.write_videofile(
        output_path, 
        codec='libx264', 
        audio=True if not is_image else False,  # Keep audio if background is video
        fps=24
    )
    
    print(f"Video saved to {output_path}")

def main():
    parser = argparse.ArgumentParser(description='Create a Loom-style circular video overlay')
    parser.add_argument('--background', required=True, help='Path to background image or video')
    parser.add_argument('--overlay', required=True, help='Path to overlay video')
    parser.add_argument('--output', required=True, help='Path to output video')
    parser.add_argument('--size', type=int, default=200, help='Height of overlay in pixels')
    parser.add_argument('--position', default='bottom-left', 
                        choices=['bottom-left', 'bottom-right', 'top-left', 'top-right'],
                        help='Position of overlay')
    
    args = parser.parse_args()
    
    # Convert position string to moviepy format
    position_map = {
        'bottom-left': ('left', 'bottom'),
        'bottom-right': ('right', 'bottom'),
        'top-left': ('left', 'top'),
        'top-right': ('right', 'top')
    }
    position = position_map[args.position]
    
    overlay_circular_video(
        args.background,
        args.overlay,
        args.output,
        args.size,
        position
    )

if __name__ == "__main__":
    main()
