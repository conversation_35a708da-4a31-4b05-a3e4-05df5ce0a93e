# Website Screen Recorder

A Python tool for recording websites using Playwright and ffmpeg. This tool allows you to record website scrolling, specific elements, or static pages.

## Prerequisites

- Python 3.6+
- Playwright
- ffmpeg (must be installed and available in your PATH)

## Installation

1. Install Python dependencies:
   ```
   pip install playwright
   playwright install
   ```

2. Make sure ffmpeg is installed:
   - Windows: Download from [ffmpeg.org](https://ffmpeg.org/download.html) and add to PATH
   - macOS: `brew install ffmpeg`
   - Ubuntu: `sudo apt install ffmpeg`

## Usage

The script supports multiple recording modes and customization options:

```
python website_recorder.py --url https://example.com --mode scroll
```

### Required Arguments

- `--url`: Website URL to record (default: https://example.com)

### Recording Modes

- `--mode scroll`: Record scrolling (default)
- `--mode element`: Record a specific element (requires `--selector`)
- `--mode static`: Record a static page

### Common Options

- `--output`: Output video filename (default: recording.mp4)
- `--width`: Viewport width (default: 1280)
- `--height`: Viewport height (default: 720)
- `--framerate`: Recording framerate (default: 30)
- `--duration`: Maximum recording duration in seconds (0 for mode-specific default)
- `--browser`: Browser to use (chromium, firefox, or webkit)
- `--headless`: Run browser in headless mode

### Scroll Mode Options

- `--scroll-steps`: Number of scroll steps (default: 10)
- `--scroll-delay`: Delay between scroll steps in seconds (default: 1.0)
- `--scroll-amount`: Scroll amount in pixels per step (default: 300)

### Element Mode Options

- `--selector`: CSS selector for the element to focus on
- `--highlight`: Highlight the selected element with a red border

## Examples

### Record a website with default scrolling

```
python website_recorder.py --url https://news.ycombinator.com
```

### Record a specific element with highlighting

```
python website_recorder.py --url https://example.com --mode element --selector ".main-article" --highlight
```

### Record in higher resolution with Firefox

```
python website_recorder.py --url https://example.com --width 1920 --height 1080 --browser firefox --output hd_recording.mp4
```

### Record a static page for 30 seconds

```
python website_recorder.py --url https://example.com --mode static --duration 30
```

## Troubleshooting

- If ffmpeg fails to start, ensure it's properly installed and available in your PATH
- On Windows, you may need to run the script as administrator for screen recording to work
- If the browser window is not visible in the recording, adjust the position of both windows
- For element mode, if the element isn't found, try using a different selector or check if the element is dynamically loaded