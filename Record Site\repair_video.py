#!/usr/bin/env python3
"""
Video Repair Utility

This script helps repair corrupted video files using the video_utils module.
It can also validate and optimize videos.

Usage:
    python repair_video.py input.mp4 [output.mp4]
    python repair_video.py --validate input.mp4
    python repair_video.py --optimize input.mp4 [output.mp4]
"""

import sys
import os
import argparse
from pathlib import Path

# Import video utilities
try:
    from video_utils import (
        validate_video_file, 
        repair_video, 
        optimize_video, 
        get_video_info,
        create_video_thumbnail
    )
except ImportError:
    print("Error: video_utils module not found. Make sure it's in the same directory.")
    sys.exit(1)


def main():
    parser = argparse.ArgumentParser(
        description="Video repair and optimization utility",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python repair_video.py corrupted.mp4                    # Repair video
  python repair_video.py corrupted.mp4 fixed.mp4         # Repair with custom output
  python repair_video.py --validate video.mp4            # Validate video
  python repair_video.py --optimize video.mp4            # Optimize video
  python repair_video.py --info video.mp4                # Get video information
  python repair_video.py --thumbnail video.mp4           # Create thumbnail
        """
    )
    
    parser.add_argument('input', help='Input video file')
    parser.add_argument('output', nargs='?', help='Output video file (optional)')
    
    parser.add_argument('--validate', action='store_true',
                        help='Validate the video file without repairing')
    parser.add_argument('--optimize', action='store_true',
                        help='Optimize the video file')
    parser.add_argument('--info', action='store_true',
                        help='Show video information')
    parser.add_argument('--thumbnail', action='store_true',
                        help='Create a thumbnail from the video')
    parser.add_argument('--quality', choices=['high', 'medium', 'low', 'web'],
                        default='medium', help='Quality preset for optimization')
    
    args = parser.parse_args()
    
    # Check if input file exists
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' not found.")
        sys.exit(1)
    
    input_path = Path(args.input)
    
    # Validate mode
    if args.validate:
        print(f"🔍 Validating video: {input_path.name}")
        is_valid = validate_video_file(str(input_path))
        if is_valid:
            print("✅ Video is valid and not corrupted")
        else:
            print("❌ Video appears to be corrupted or invalid")
        return
    
    # Info mode
    if args.info:
        print(f"📊 Getting video information: {input_path.name}")
        info = get_video_info(str(input_path))
        if info:
            print("\n📹 Video Information:")
            
            # Format information
            format_info = info.get('format', {})
            print(f"  Format: {format_info.get('format_name', 'Unknown')}")
            print(f"  Duration: {float(format_info.get('duration', 0)):.2f} seconds")
            print(f"  Size: {int(format_info.get('size', 0)) / (1024*1024):.2f} MB")
            print(f"  Bitrate: {int(format_info.get('bit_rate', 0)) / 1000:.0f} kbps")
            
            # Video stream information
            video_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'video']
            if video_streams:
                stream = video_streams[0]
                print(f"\n🎬 Video Stream:")
                print(f"  Codec: {stream.get('codec_name', 'Unknown')}")
                print(f"  Resolution: {stream.get('width', 'Unknown')}x{stream.get('height', 'Unknown')}")
                print(f"  Frame Rate: {stream.get('r_frame_rate', 'Unknown')}")
                print(f"  Pixel Format: {stream.get('pix_fmt', 'Unknown')}")
            
            # Audio stream information
            audio_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'audio']
            if audio_streams:
                stream = audio_streams[0]
                print(f"\n🔊 Audio Stream:")
                print(f"  Codec: {stream.get('codec_name', 'Unknown')}")
                print(f"  Sample Rate: {stream.get('sample_rate', 'Unknown')} Hz")
                print(f"  Channels: {stream.get('channels', 'Unknown')}")
        else:
            print("❌ Could not extract video information")
        return
    
    # Thumbnail mode
    if args.thumbnail:
        print(f"🖼️ Creating thumbnail: {input_path.name}")
        output_path = args.output or f"{input_path.stem}_thumbnail.jpg"
        result = create_video_thumbnail(str(input_path), output_path)
        if result:
            print(f"✅ Thumbnail created: {result}")
        else:
            print("❌ Failed to create thumbnail")
        return
    
    # Optimize mode
    if args.optimize:
        print(f"⚡ Optimizing video: {input_path.name}")
        output_path = args.output or f"{input_path.stem}_optimized{input_path.suffix}"
        
        print(f"Using quality preset: {args.quality}")
        result = optimize_video(str(input_path), output_path, quality=args.quality)
        
        if result:
            print(f"✅ Video optimized successfully: {result}")
            
            # Show size comparison
            original_size = os.path.getsize(str(input_path)) / (1024 * 1024)
            optimized_size = os.path.getsize(result) / (1024 * 1024)
            reduction = ((original_size - optimized_size) / original_size) * 100
            
            print(f"📊 Size comparison:")
            print(f"  Original: {original_size:.2f} MB")
            print(f"  Optimized: {optimized_size:.2f} MB")
            print(f"  Reduction: {reduction:.1f}%")
        else:
            print("❌ Video optimization failed")
        return
    
    # Default: Repair mode
    print(f"🔧 Repairing video: {input_path.name}")
    
    # First, check if the video is actually corrupted
    print("Checking if video needs repair...")
    is_valid = validate_video_file(str(input_path), min_duration=0.1)
    
    if is_valid:
        print("✅ Video appears to be valid. No repair needed.")
        print("Use --optimize flag if you want to optimize the video instead.")
        return
    
    print("❌ Video appears to be corrupted. Attempting repair...")
    
    # Determine output path
    output_path = args.output or f"{input_path.stem}_repaired{input_path.suffix}"
    
    # Attempt repair
    result = repair_video(str(input_path), output_path)
    
    if result:
        print(f"✅ Video repair completed: {result}")
        
        # Validate the repaired video
        print("Validating repaired video...")
        if validate_video_file(result):
            print("✅ Repaired video is valid")
        else:
            print("⚠️ Repaired video may still have issues")
    else:
        print("❌ Video repair failed")
        print("\nTroubleshooting tips:")
        print("- The video file may be too severely corrupted")
        print("- Try using a different video player to check if it's actually corrupted")
        print("- Consider using --optimize instead if the video plays but has issues")


if __name__ == "__main__":
    main()
