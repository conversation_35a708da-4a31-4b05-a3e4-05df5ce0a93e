#!/usr/bin/env python3
"""
Batch Loom-Style Video Generator for B2B Cold Email Campaigns

This script processes multiple prospects at once, creating personalized 
Loom-style videos for each prospect's website.

Usage:
    python batch_loom_generator.py --prospects prospects.json --avatar speaker.mp4 --template template.json
"""

import os
import sys
import json
import argparse
import time
from pathlib import Path
import concurrent.futures
from datetime import datetime

# Import the main generator
try:
    from loom_style_generator import create_loom_style_video, record_website_background
    from Record_Site.video_utils import validate_video_file, optimize_video
except ImportError as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)


def load_prospects(prospects_file):
    """Load prospects from JSON file"""
    try:
        with open(prospects_file, 'r') as f:
            prospects = json.load(f)
        
        # Validate prospect data
        required_fields = ['name', 'website', 'email']
        for i, prospect in enumerate(prospects):
            for field in required_fields:
                if field not in prospect:
                    raise ValueError(f"Prospect {i+1} missing required field: {field}")
        
        print(f"✅ Loaded {len(prospects)} prospects")
        return prospects
        
    except Exception as e:
        print(f"❌ Error loading prospects: {e}")
        return None


def load_template(template_file):
    """Load video generation template"""
    try:
        with open(template_file, 'r') as f:
            template = json.load(f)
        
        print(f"✅ Loaded template: {template.get('name', 'Unnamed')}")
        return template
        
    except Exception as e:
        print(f"❌ Error loading template: {e}")
        return None


def create_prospect_video(prospect, template, avatar_path, output_dir, audio_path=None):
    """Create a video for a single prospect"""
    try:
        prospect_name = prospect['name'].replace(' ', '_').replace('.', '_')
        output_file = os.path.join(output_dir, f"{prospect_name}_demo.mp4")
        
        print(f"\n🎬 Processing: {prospect['name']} ({prospect['website']})")
        
        # Get template settings
        settings = template.get('settings', {})
        
        # Record website
        temp_background = os.path.join(output_dir, f"{prospect_name}_bg_temp.mp4")
        
        recording_kwargs = {
            'width': settings.get('width', 1280),
            'height': settings.get('height', 720),
            'scroll_steps': settings.get('scroll_steps', 5),
            'scroll_delay': settings.get('scroll_delay', 2.0),
            'selector': settings.get('selector'),
            'highlight': settings.get('highlight', False)
        }
        
        success = record_website_background(
            prospect['website'], 
            settings.get('duration', 0),
            temp_background,
            settings.get('mode', 'scroll'),
            **recording_kwargs
        )
        
        if not success:
            print(f"❌ Failed to record website for {prospect['name']}")
            return None
        
        # Create Loom-style video
        position_map = {
            'bottom-left': ('left', 'bottom'),
            'bottom-right': ('right', 'bottom'),
            'top-left': ('left', 'top'),
            'top-right': ('right', 'top')
        }
        position = position_map.get(settings.get('position', 'bottom-right'), ('right', 'bottom'))
        
        success = create_loom_style_video(
            temp_background,
            avatar_path,
            output_file,
            avatar_size=settings.get('avatar_size', 200),
            position=position,
            audio_path=audio_path,
            audio_volume=settings.get('audio_volume', 1.0)
        )
        
        # Clean up temporary background
        try:
            os.remove(temp_background)
        except:
            pass
        
        if success and validate_video_file(output_file):
            print(f"✅ Created video for {prospect['name']}: {output_file}")
            
            # Add prospect info to result
            return {
                'prospect': prospect,
                'video_path': output_file,
                'status': 'success',
                'timestamp': datetime.now().isoformat()
            }
        else:
            print(f"❌ Failed to create video for {prospect['name']}")
            return {
                'prospect': prospect,
                'video_path': None,
                'status': 'failed',
                'timestamp': datetime.now().isoformat()
            }
            
    except Exception as e:
        print(f"❌ Error processing {prospect.get('name', 'Unknown')}: {e}")
        return {
            'prospect': prospect,
            'video_path': None,
            'status': 'error',
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }


def generate_report(results, output_dir):
    """Generate a summary report"""
    try:
        report_file = os.path.join(output_dir, 'batch_report.json')
        
        # Calculate statistics
        total = len(results)
        successful = len([r for r in results if r['status'] == 'success'])
        failed = total - successful
        
        report = {
            'summary': {
                'total_prospects': total,
                'successful': successful,
                'failed': failed,
                'success_rate': f"{(successful/total*100):.1f}%" if total > 0 else "0%",
                'generated_at': datetime.now().isoformat()
            },
            'results': results
        }
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📊 Batch Report:")
        print(f"  Total prospects: {total}")
        print(f"  Successful: {successful}")
        print(f"  Failed: {failed}")
        print(f"  Success rate: {report['summary']['success_rate']}")
        print(f"  Report saved: {report_file}")
        
        return report_file
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        return None


def create_sample_prospects():
    """Create a sample prospects.json file"""
    sample_prospects = [
        {
            "name": "John Smith",
            "email": "<EMAIL>",
            "website": "https://example.com",
            "company": "Tech Startup Inc",
            "notes": "Interested in automation solutions"
        },
        {
            "name": "Sarah Johnson",
            "email": "<EMAIL>", 
            "website": "https://news.ycombinator.com",
            "company": "Creative Design Agency",
            "notes": "Looking for design tools"
        },
        {
            "name": "Mike Chen",
            "email": "<EMAIL>",
            "website": "https://httpbin.org",
            "company": "E-commerce Solutions",
            "notes": "Needs better analytics"
        }
    ]
    
    with open('prospects_sample.json', 'w') as f:
        json.dump(sample_prospects, f, indent=2)
    
    print("✅ Created sample prospects file: prospects_sample.json")


def create_sample_template():
    """Create a sample template.json file"""
    sample_template = {
        "name": "B2B Cold Email Template",
        "description": "Standard template for B2B cold email videos",
        "settings": {
            "mode": "scroll",
            "duration": 15,
            "width": 1280,
            "height": 720,
            "avatar_size": 200,
            "position": "bottom-right",
            "audio_volume": 1.0,
            "scroll_steps": 5,
            "scroll_delay": 2.0,
            "highlight": False,
            "quality": "medium"
        },
        "created_at": datetime.now().isoformat()
    }
    
    with open('template_sample.json', 'w') as f:
        json.dump(sample_template, f, indent=2)
    
    print("✅ Created sample template file: template_sample.json")


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Batch generate Loom-style videos for B2B cold emails",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Process all prospects
  python batch_loom_generator.py --prospects prospects.json --avatar speaker.mp4 --template template.json
  
  # With external audio
  python batch_loom_generator.py --prospects prospects.json --avatar headshot.jpg --audio voiceover.mp3 --template template.json
  
  # Create sample files
  python batch_loom_generator.py --create-samples
        """
    )
    
    parser.add_argument('--prospects', help='JSON file with prospect data')
    parser.add_argument('--avatar', help='Avatar video or image file')
    parser.add_argument('--template', help='JSON template file with settings')
    parser.add_argument('--audio', help='External audio file (optional)')
    parser.add_argument('--output-dir', default='batch_output', help='Output directory')
    parser.add_argument('--max-workers', type=int, default=2, help='Maximum parallel workers')
    parser.add_argument('--create-samples', action='store_true', help='Create sample files and exit')
    
    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()
    
    print("🎬 Batch Loom-Style Video Generator")
    print("=" * 50)
    
    # Create sample files if requested
    if args.create_samples:
        create_sample_prospects()
        create_sample_template()
        print("\n✅ Sample files created. Edit them and run again with actual data.")
        return 0
    
    # Validate required arguments
    if not all([args.prospects, args.avatar, args.template]):
        print("❌ Error: --prospects, --avatar, and --template are required")
        print("Use --create-samples to generate sample files")
        return 1
    
    # Validate files exist
    for file_path in [args.prospects, args.avatar, args.template]:
        if not os.path.exists(file_path):
            print(f"❌ Error: File not found: {file_path}")
            return 1
    
    if args.audio and not os.path.exists(args.audio):
        print(f"❌ Error: Audio file not found: {args.audio}")
        return 1
    
    # Load data
    prospects = load_prospects(args.prospects)
    template = load_template(args.template)
    
    if not prospects or not template:
        return 1
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    print(f"📁 Output directory: {args.output_dir}")
    
    # Process prospects
    print(f"\n🚀 Starting batch processing with {args.max_workers} workers...")
    start_time = time.time()
    
    results = []
    
    # Process prospects (sequential for now to avoid overwhelming the system)
    for i, prospect in enumerate(prospects, 1):
        print(f"\n[{i}/{len(prospects)}] Processing {prospect['name']}...")
        
        result = create_prospect_video(
            prospect, template, args.avatar, args.output_dir, args.audio
        )
        
        if result:
            results.append(result)
    
    # Generate report
    elapsed_time = time.time() - start_time
    print(f"\n⏱️ Total processing time: {elapsed_time:.1f} seconds")
    
    report_file = generate_report(results, args.output_dir)
    
    # Show successful videos
    successful_videos = [r for r in results if r['status'] == 'success']
    if successful_videos:
        print(f"\n🎉 Successfully created {len(successful_videos)} videos:")
        for result in successful_videos:
            print(f"  • {result['prospect']['name']}: {result['video_path']}")
    
    print(f"\n✅ Batch processing complete! Check {args.output_dir} for results.")
    return 0


if __name__ == "__main__":
    sys.exit(main())
