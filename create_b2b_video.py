#!/usr/bin/env python3
"""
Simple B2B Cold Email Video Creator

This script creates Loom-style videos by combining:
1. Website recording (using existing website_recorder.py)
2. Circular avatar overlay (using existing overlay functionality)
3. Audio management

Usage:
    python create_b2b_video.py --url https://prospect.com --avatar speaker.mp4 --output prospect_demo.mp4
"""

import os
import sys
import argparse
import subprocess
import tempfile
import shutil
from pathlib import Path


def record_website(url, output_path, duration=15, mode='scroll', width=1280, height=720):
    """Record a website using the existing website recorder"""
    try:
        print(f"🌐 Recording website: {url}")
        
        cmd = [
            sys.executable,
            os.path.join('Record Site', 'website_recorder.py'),
            '--url', url,
            '--mode', mode,
            '--output', output_path,
            '--headless',
            '--width', str(width),
            '--height', str(height)
        ]
        
        if duration > 0:
            cmd.extend(['--duration', str(duration)])
        
        if mode == 'scroll':
            cmd.extend(['--scroll-steps', '5', '--scroll-delay', '2.0'])
        
        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ Website recorded: {output_path}")
            return True
        else:
            print(f"❌ Website recording failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error recording website: {e}")
        return False


def create_overlay_video(background_path, avatar_path, output_path, avatar_size=200, position="bottom-right"):
    """Create overlay video using the existing overlay functionality"""
    try:
        print(f"🎭 Creating overlay video...")
        print(f"Background: {os.path.basename(background_path)}")
        print(f"Avatar: {os.path.basename(avatar_path)}")
        
        # Import the overlay function
        sys.path.insert(0, 'Overlay')
        from easy_overlay import create_overlay
        
        # Map position names to tuple format
        position_map = {
            "bottom-right": ("right", "bottom"),
            "bottom-left": ("left", "bottom"),
            "top-right": ("right", "top"),
            "top-left": ("left", "top")
        }
        
        pos_tuple = position_map.get(position, ("right", "bottom"))
        
        # Create the overlay
        success = create_overlay(
            background_path=background_path,
            overlay_path=avatar_path,
            output_path=output_path,
            overlay_size=avatar_size,
            position=pos_tuple
        )
        
        if success:
            print(f"✅ Overlay video created: {output_path}")
            return True
        else:
            print(f"❌ Overlay creation failed")
            return False
            
    except Exception as e:
        print(f"❌ Error creating overlay: {e}")
        return False


def add_external_audio(video_path, audio_path, output_path, volume=1.0):
    """Add external audio to video using FFmpeg"""
    try:
        print(f"🔊 Adding external audio...")
        
        # Get FFmpeg path
        sys.path.insert(0, 'Record Site')
        from video_utils import get_ffmpeg_path
        
        ffmpeg_path = get_ffmpeg_path()
        
        cmd = [
            ffmpeg_path,
            '-i', video_path,
            '-i', audio_path,
            '-c:v', 'copy',
            '-c:a', 'aac',
            '-map', '0:v:0',
            '-map', '1:a:0',
            '-shortest',  # Stop when shortest input ends
            '-y',
            output_path
        ]
        
        if volume != 1.0:
            cmd.insert(-2, '-filter:a')
            cmd.insert(-2, f'volume={volume}')
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ Audio added successfully: {output_path}")
            return True
        else:
            print(f"❌ Audio addition failed: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Error adding audio: {e}")
        return False


def validate_and_optimize(video_path):
    """Validate and optimize the final video"""
    try:
        print(f"🔍 Validating and optimizing video...")
        
        sys.path.insert(0, 'Record Site')
        from video_utils import validate_video_file, optimize_video
        
        # Validate
        if not validate_video_file(video_path):
            print(f"❌ Video validation failed")
            return False
        
        # Optimize
        temp_output = video_path + '.optimized'
        optimized = optimize_video(video_path, temp_output, quality='medium')
        
        if optimized and validate_video_file(optimized):
            # Replace original with optimized
            shutil.move(optimized, video_path)
            print(f"✅ Video optimized successfully")
        else:
            print(f"⚠️ Optimization failed, keeping original")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in validation/optimization: {e}")
        return False


def create_b2b_video(url, avatar_path, output_path, **kwargs):
    """Create a complete B2B cold email video"""
    
    # Extract parameters
    duration = kwargs.get('duration', 15)
    mode = kwargs.get('mode', 'scroll')
    width = kwargs.get('width', 1280)
    height = kwargs.get('height', 720)
    avatar_size = kwargs.get('avatar_size', 200)
    position = kwargs.get('position', 'bottom-right')
    audio_path = kwargs.get('audio_path')
    audio_volume = kwargs.get('audio_volume', 1.0)
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp(prefix='b2b_video_')
    background_video = os.path.join(temp_dir, 'background.mp4')
    overlay_video = os.path.join(temp_dir, 'overlay.mp4')
    
    try:
        print("🎬 B2B Cold Email Video Creator")
        print("=" * 50)
        
        # Step 1: Record website
        print(f"\n📹 Step 1: Recording website background")
        if not record_website(url, background_video, duration, mode, width, height):
            return False
        
        # Step 2: Create overlay
        print(f"\n🎭 Step 2: Adding avatar overlay")
        final_output = overlay_video if not audio_path else os.path.join(temp_dir, 'with_overlay.mp4')
        
        if not create_overlay_video(background_video, avatar_path, final_output, avatar_size, position):
            return False
        
        # Step 3: Add external audio if provided
        if audio_path:
            print(f"\n🔊 Step 3: Adding external audio")
            if not add_external_audio(final_output, audio_path, overlay_video, audio_volume):
                return False
            final_output = overlay_video
        
        # Step 4: Move to final location and optimize
        print(f"\n⚡ Step 4: Finalizing video")
        shutil.move(final_output, output_path)
        
        if not validate_and_optimize(output_path):
            return False
        
        # Show final info
        try:
            sys.path.insert(0, 'Record Site')
            from video_utils import get_video_info
            
            info = get_video_info(output_path)
            if info:
                format_info = info.get('format', {})
                duration_final = float(format_info.get('duration', 0))
                size_mb = int(format_info.get('size', 0)) / (1024 * 1024)
                print(f"📊 Final video: {duration_final:.1f}s, {size_mb:.2f}MB")
        except:
            pass
        
        print(f"\n🎉 Success! B2B video created: {output_path}")
        return True
        
    except Exception as e:
        print(f"\n❌ Error creating B2B video: {e}")
        return False
        
    finally:
        # Clean up temporary directory
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Create B2B cold email videos with website recording and avatar overlay",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic video with avatar
  python create_b2b_video.py --url https://prospect.com --avatar speaker.mp4 --output demo.mp4
  
  # With external audio
  python create_b2b_video.py --url https://prospect.com --avatar headshot.jpg --audio voiceover.mp3 --output demo.mp4
  
  # Custom settings
  python create_b2b_video.py --url https://prospect.com --avatar speaker.mp4 --duration 20 --avatar-size 250 --position top-right --output demo.mp4
        """
    )
    
    # Required arguments
    parser.add_argument('--url', required=True, help='Website URL to record')
    parser.add_argument('--avatar', required=True, help='Avatar video or image file')
    parser.add_argument('--output', required=True, help='Output video file')
    
    # Optional arguments
    parser.add_argument('--audio', help='External audio file (MP3, WAV, etc.)')
    parser.add_argument('--duration', type=int, default=15, help='Recording duration in seconds')
    parser.add_argument('--mode', choices=['scroll', 'static', 'element'], default='scroll', help='Recording mode')
    parser.add_argument('--avatar-size', type=int, default=200, help='Avatar size in pixels')
    parser.add_argument('--position', choices=['bottom-left', 'bottom-right', 'top-left', 'top-right'], 
                        default='bottom-right', help='Avatar position')
    parser.add_argument('--audio-volume', type=float, default=1.0, help='Audio volume multiplier')
    parser.add_argument('--width', type=int, default=1280, help='Video width')
    parser.add_argument('--height', type=int, default=720, help='Video height')
    
    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()
    
    # Validate inputs
    if not os.path.exists(args.avatar):
        print(f"❌ Error: Avatar file not found: {args.avatar}")
        return 1
    
    if args.audio and not os.path.exists(args.audio):
        print(f"❌ Error: Audio file not found: {args.audio}")
        return 1
    
    # Create video
    success = create_b2b_video(
        url=args.url,
        avatar_path=args.avatar,
        output_path=args.output,
        duration=args.duration,
        mode=args.mode,
        width=args.width,
        height=args.height,
        avatar_size=args.avatar_size,
        position=args.position,
        audio_path=args.audio,
        audio_volume=args.audio_volume
    )
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
