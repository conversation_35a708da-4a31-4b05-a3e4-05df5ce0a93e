"""
Video utility functions for the website recorder.
Provides video validation, optimization, and repair capabilities.
"""

import os
import subprocess
import json
import tempfile
import shutil
from pathlib import Path


def get_ffmpeg_path():
    """Get the path to the ffmpeg executable"""
    # First check the local ffmpeg directory
    script_dir = Path(os.path.dirname(os.path.abspath(__file__)))
    local_ffmpeg = script_dir / "ffmpeg" / "bin" / "ffmpeg.exe"
    
    if local_ffmpeg.exists():
        return str(local_ffmpeg)
    
    # Fallback to system PATH
    return "ffmpeg"


def get_ffprobe_path():
    """Get the path to the ffprobe executable"""
    ffmpeg_path = get_ffmpeg_path()
    if ffmpeg_path.endswith('ffmpeg.exe'):
        return ffmpeg_path.replace('ffmpeg.exe', 'ffprobe.exe')
    elif ffmpeg_path.endswith('ffmpeg'):
        return ffmpeg_path.replace('ffmpeg', 'ffprobe')
    else:
        return 'ffprobe'


def get_video_info(video_path):
    """Get detailed information about a video file"""
    try:
        ffprobe_path = get_ffprobe_path()
        
        probe_cmd = [
            ffprobe_path,
            '-v', 'quiet',
            '-print_format', 'json',
            '-show_format',
            '-show_streams',
            video_path
        ]
        
        result = subprocess.run(probe_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode != 0:
            return None
            
        return json.loads(result.stdout)
        
    except Exception as e:
        print(f"Error getting video info: {e}")
        return None


def validate_video_file(video_path, min_duration=1.0, check_corruption=True):
    """Comprehensive video file validation"""
    try:
        if not os.path.exists(video_path):
            print(f"Video file does not exist: {video_path}")
            return False
            
        if os.path.getsize(video_path) == 0:
            print("Video file is empty")
            return False
        
        probe_data = get_video_info(video_path)
        if not probe_data:
            print("Could not read video file information")
            return False
        
        # Check if we have video streams
        video_streams = [s for s in probe_data.get('streams', []) if s.get('codec_type') == 'video']
        if not video_streams:
            print("No video streams found in file")
            return False
            
        # Check duration
        duration = float(probe_data.get('format', {}).get('duration', 0))
        if duration < min_duration:
            print(f"Video duration {duration}s is less than minimum {min_duration}s")
            return False
            
        # Check for valid dimensions
        video_stream = video_streams[0]
        width = video_stream.get('width', 0)
        height = video_stream.get('height', 0)
        if width <= 0 or height <= 0:
            print("Invalid video dimensions")
            return False
        
        # Check for corruption if requested
        if check_corruption:
            if not check_video_corruption(video_path):
                print("Video file appears to be corrupted")
                return False
            
        print(f"Video validation passed: {duration:.2f}s, {width}x{height}")
        return True
        
    except Exception as e:
        print(f"Error validating video: {e}")
        return False


def check_video_corruption(video_path):
    """Check if video file is corrupted by attempting to decode a few frames"""
    try:
        ffmpeg_path = get_ffmpeg_path()
        
        # Try to decode first 5 seconds to null output
        check_cmd = [
            ffmpeg_path,
            '-v', 'error',
            '-i', video_path,
            '-t', '5',
            '-f', 'null',
            '-'
        ]
        
        result = subprocess.run(check_cmd, capture_output=True, text=True, timeout=30)
        
        # If there are errors in stderr, the video might be corrupted
        if result.stderr and ('error' in result.stderr.lower() or 'corrupt' in result.stderr.lower()):
            return False
            
        return result.returncode == 0
        
    except Exception as e:
        print(f"Error checking video corruption: {e}")
        return False


def optimize_video(input_path, output_path=None, quality='medium', target_size_mb=None):
    """Optimize video file for better compatibility and size"""
    try:
        if output_path is None:
            name, ext = os.path.splitext(input_path)
            output_path = f"{name}_optimized{ext}"
            
        ffmpeg_path = get_ffmpeg_path()
        
        # Quality presets
        quality_settings = {
            'high': ['-crf', '18', '-preset', 'slow'],
            'medium': ['-crf', '23', '-preset', 'medium'],
            'low': ['-crf', '28', '-preset', 'fast'],
            'web': ['-crf', '25', '-preset', 'medium', '-tune', 'film']
        }
        
        settings = quality_settings.get(quality, quality_settings['medium'])
        
        # Base command
        optimize_cmd = [
            ffmpeg_path,
            '-i', input_path,
            '-c:v', 'libx264',
            '-pix_fmt', 'yuv420p',
            '-movflags', '+faststart',  # Enable fast start for web playback
            '-avoid_negative_ts', 'make_zero',  # Fix timestamp issues
        ]
        
        # Add quality settings
        optimize_cmd.extend(settings)
        
        # Target file size if specified
        if target_size_mb:
            # Calculate bitrate for target size
            video_info = get_video_info(input_path)
            if video_info:
                duration = float(video_info.get('format', {}).get('duration', 0))
                if duration > 0:
                    target_bitrate = int((target_size_mb * 8 * 1024) / duration)  # kbps
                    optimize_cmd.extend(['-b:v', f'{target_bitrate}k', '-maxrate', f'{target_bitrate * 1.5}k', '-bufsize', f'{target_bitrate * 2}k'])
        
        # Add output file
        optimize_cmd.extend(['-y', output_path])
        
        print(f"Optimizing video with {quality} quality...")
        if target_size_mb:
            print(f"Target size: {target_size_mb}MB")
            
        result = subprocess.run(optimize_cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print(f"Video optimized successfully: {output_path}")
            return output_path
        else:
            print(f"Video optimization failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"Error optimizing video: {e}")
        return None


def repair_video(input_path, output_path=None):
    """Attempt to repair a corrupted video file"""
    try:
        if output_path is None:
            name, ext = os.path.splitext(input_path)
            output_path = f"{name}_repaired{ext}"
            
        ffmpeg_path = get_ffmpeg_path()
        
        # Try to repair by re-encoding with error resilience
        repair_cmd = [
            ffmpeg_path,
            '-err_detect', 'ignore_err',
            '-i', input_path,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts',
            '-y',
            output_path
        ]
        
        print(f"Attempting to repair video: {input_path}")
        result = subprocess.run(repair_cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0 and validate_video_file(output_path, min_duration=0.1):
            print(f"Video repaired successfully: {output_path}")
            return output_path
        else:
            print(f"Video repair failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"Error repairing video: {e}")
        return None


def convert_video_format(input_path, output_path, format_type='mp4'):
    """Convert video to different format"""
    try:
        ffmpeg_path = get_ffmpeg_path()
        
        format_settings = {
            'mp4': ['-c:v', 'libx264', '-c:a', 'aac'],
            'webm': ['-c:v', 'libvpx-vp9', '-c:a', 'libvorbis'],
            'avi': ['-c:v', 'libx264', '-c:a', 'mp3'],
            'mov': ['-c:v', 'libx264', '-c:a', 'aac']
        }
        
        settings = format_settings.get(format_type, format_settings['mp4'])
        
        convert_cmd = [
            ffmpeg_path,
            '-i', input_path,
        ] + settings + [
            '-movflags', '+faststart',
            '-y',
            output_path
        ]
        
        print(f"Converting video to {format_type.upper()} format...")
        result = subprocess.run(convert_cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"Video converted successfully: {output_path}")
            return output_path
        else:
            print(f"Video conversion failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"Error converting video: {e}")
        return None


def create_video_thumbnail(video_path, output_path=None, timestamp='00:00:01'):
    """Create a thumbnail image from video"""
    try:
        if output_path is None:
            name = os.path.splitext(video_path)[0]
            output_path = f"{name}_thumbnail.jpg"
            
        ffmpeg_path = get_ffmpeg_path()
        
        thumbnail_cmd = [
            ffmpeg_path,
            '-i', video_path,
            '-ss', timestamp,
            '-vframes', '1',
            '-q:v', '2',
            '-y',
            output_path
        ]
        
        result = subprocess.run(thumbnail_cmd, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(f"Thumbnail created: {output_path}")
            return output_path
        else:
            print(f"Thumbnail creation failed: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"Error creating thumbnail: {e}")
        return None
