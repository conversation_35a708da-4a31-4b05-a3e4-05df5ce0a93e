"""
<PERSON><PERSON><PERSON> to run the circular video overlay with the specified files.
This script doesn't rely on importing the main module, but includes the necessary code directly.
"""
import numpy as np
import cv2
import os
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip

def circular_mask(frame):
    """Apply a circular mask to a frame, making everything outside the circle transparent."""
    height, width = frame.shape[:2]
    mask = np.zeros((height, width), dtype=np.uint8)
    center = (width // 2, height // 2)
    radius = min(center)  # full circle in square
    cv2.circle(mask, center, radius, 255, -1)

    # Convert to BGRA to handle transparency
    if frame.shape[2] == 3:  # RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGRA)
    # Set alpha channel based on mask
    frame[:, :, 3] = mask
    return frame

def process_files():
    # File paths - update these to the correct locations
    background_path = "high-quality-roofing.png"
    overlay_path = "CJ.mp4"
    output_path = "output_with_overlay.mp4"
    
    # Check if files exist
    if not os.path.exists(background_path):
        print(f"Warning: Background file '{background_path}' not found.")
        print("Please place the file in the same directory as this script or update the path.")
        return
        
    if not os.path.exists(overlay_path):
        print(f"Warning: Overlay file '{overlay_path}' not found.")
        print("Please place the file in the same directory as this script or update the path.")
        return
    
    print(f"Processing overlay of '{overlay_path}' onto '{background_path}'...")
    
    try:
        # Load overlay video
        overlay = VideoFileClip(overlay_path)
        
        # Determine if background is image or video
        bg_ext = os.path.splitext(background_path)[1].lower()
        is_image = bg_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        # Load background
        if is_image:
            # Load static image and get video duration from overlay
            bg = ImageClip(background_path, duration=overlay.duration)
        else:
            # Both are videos
            bg = VideoFileClip(background_path)
            
            # If overlay is longer than background, trim it
            if overlay.duration > bg.duration:
                overlay = overlay.subclip(0, bg.duration)
        
        # Resize overlay and apply circular mask
        overlay_size = 200  # Height in pixels
        position = ("right", "bottom")  # Position of overlay
        
        overlay = overlay.resize(height=overlay_size)
        overlay = overlay.fl_image(circular_mask).set_position(position)
        
        # Combine clips
        final = CompositeVideoClip([bg, overlay])
        
        # Write output file with appropriate settings
        final.write_videofile(
            output_path, 
            codec='libx264', 
            audio=True if not is_image else False,
            fps=24
        )
        
        print(f"Overlay completed! Output saved to: {output_path}")
        
    except Exception as e:
        print(f"Error processing files: {str(e)}")
        print("Make sure you have installed all required dependencies:")
        print("pip install moviepy opencv-python numpy")

if __name__ == "__main__":
    try:
        import moviepy
        process_files()
    except ImportError:
        print("MoviePy is not installed. Please install the required dependencies:")
        print("pip install moviepy opencv-python numpy")
        print("\nOr run this command to install from requirements.txt:")
        print("pip install -r requirements.txt")
