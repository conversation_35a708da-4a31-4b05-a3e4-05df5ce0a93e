"""
Test script for the improved video recording functionality.
This script tests various video recording modes and validates the output.
"""

import os
import sys
import subprocess
import tempfile
import shutil
from pathlib import Path

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from video_utils import validate_video_file, optimize_video, get_video_info, repair_video
    print("✓ Video utilities imported successfully")
except ImportError as e:
    print(f"✗ Failed to import video utilities: {e}")
    sys.exit(1)


def test_video_validation():
    """Test video validation functionality"""
    print("\n=== Testing Video Validation ===")
    
    # Test with non-existent file
    result = validate_video_file("nonexistent.mp4")
    print(f"Non-existent file validation: {'✓ PASS' if not result else '✗ FAIL'}")
    
    # Test with existing video files in the directory
    video_files = []
    for ext in ['.mp4', '.avi', '.mov', '.webm']:
        for file in Path('.').glob(f'*{ext}'):
            video_files.append(file)
    
    if video_files:
        for video_file in video_files[:3]:  # Test first 3 video files
            result = validate_video_file(str(video_file))
            print(f"Validation of {video_file.name}: {'✓ PASS' if result else '✗ FAIL'}")
    else:
        print("No video files found for validation testing")


def test_video_info():
    """Test video information extraction"""
    print("\n=== Testing Video Information Extraction ===")
    
    video_files = list(Path('.').glob('*.mp4'))
    if video_files:
        video_file = video_files[0]
        info = get_video_info(str(video_file))
        if info:
            print(f"✓ Successfully extracted info from {video_file.name}")
            # Print some basic info
            format_info = info.get('format', {})
            print(f"  Duration: {format_info.get('duration', 'Unknown')} seconds")
            print(f"  Size: {format_info.get('size', 'Unknown')} bytes")
            
            video_streams = [s for s in info.get('streams', []) if s.get('codec_type') == 'video']
            if video_streams:
                stream = video_streams[0]
                print(f"  Resolution: {stream.get('width', 'Unknown')}x{stream.get('height', 'Unknown')}")
                print(f"  Codec: {stream.get('codec_name', 'Unknown')}")
        else:
            print(f"✗ Failed to extract info from {video_file.name}")
    else:
        print("No MP4 files found for info extraction testing")


def test_website_recording():
    """Test the website recording functionality"""
    print("\n=== Testing Website Recording ===")
    
    # Test with a simple static recording
    test_url = "https://example.com"
    output_file = "test_recording.mp4"
    
    try:
        # Clean up any existing test file
        if os.path.exists(output_file):
            os.remove(output_file)
        
        print(f"Testing static recording of {test_url}...")
        
        # Run the website recorder
        cmd = [
            sys.executable, "website_recorder.py",
            "--url", test_url,
            "--mode", "static",
            "--duration", "5",
            "--output", output_file,
            "--headless"
        ]
        
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✓ Website recording command completed successfully")
            
            # Check if output file was created
            if os.path.exists(output_file):
                print(f"✓ Output file created: {output_file}")
                
                # Validate the video
                if validate_video_file(output_file, min_duration=3.0):
                    print("✓ Video validation passed")
                    
                    # Get file size
                    size_mb = os.path.getsize(output_file) / (1024 * 1024)
                    print(f"  File size: {size_mb:.2f} MB")
                    
                    return True
                else:
                    print("✗ Video validation failed")
            else:
                print(f"✗ Output file not created: {output_file}")
        else:
            print(f"✗ Website recording failed: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("✗ Website recording timed out")
    except Exception as e:
        print(f"✗ Website recording error: {e}")
    
    finally:
        # Clean up test file
        if os.path.exists(output_file):
            try:
                os.remove(output_file)
                print(f"Cleaned up test file: {output_file}")
            except:
                pass
    
    return False


def test_video_optimization():
    """Test video optimization functionality"""
    print("\n=== Testing Video Optimization ===")
    
    # Look for existing video files to optimize
    video_files = list(Path('.').glob('*.mp4'))
    if not video_files:
        print("No MP4 files found for optimization testing")
        return
    
    test_video = video_files[0]
    optimized_output = f"test_optimized_{test_video.name}"
    
    try:
        print(f"Testing optimization of {test_video.name}...")
        
        # Test optimization
        result = optimize_video(str(test_video), optimized_output, quality='low')
        
        if result and os.path.exists(result):
            print(f"✓ Video optimization successful: {result}")
            
            # Compare file sizes
            original_size = os.path.getsize(test_video) / (1024 * 1024)
            optimized_size = os.path.getsize(result) / (1024 * 1024)
            
            print(f"  Original size: {original_size:.2f} MB")
            print(f"  Optimized size: {optimized_size:.2f} MB")
            print(f"  Size reduction: {((original_size - optimized_size) / original_size * 100):.1f}%")
            
            # Validate optimized video
            if validate_video_file(result):
                print("✓ Optimized video validation passed")
            else:
                print("✗ Optimized video validation failed")
                
        else:
            print("✗ Video optimization failed")
            
    except Exception as e:
        print(f"✗ Video optimization error: {e}")
    
    finally:
        # Clean up test file
        if os.path.exists(optimized_output):
            try:
                os.remove(optimized_output)
                print(f"Cleaned up test file: {optimized_output}")
            except:
                pass


def main():
    """Run all tests"""
    print("🎬 Video Recording System Test Suite")
    print("=" * 50)
    
    # Check if ffmpeg is available
    try:
        from video_utils import get_ffmpeg_path
        ffmpeg_path = get_ffmpeg_path()
        result = subprocess.run([ffmpeg_path, '-version'], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✓ FFmpeg found at: {ffmpeg_path}")
        else:
            print(f"✗ FFmpeg not working properly")
            return
    except Exception as e:
        print(f"✗ FFmpeg check failed: {e}")
        return
    
    # Run tests
    test_video_validation()
    test_video_info()
    test_video_optimization()
    
    # Test website recording (this takes longer)
    print("\nDo you want to test website recording? This will take about 30 seconds. (y/n): ", end="")
    try:
        response = input().strip().lower()
        if response in ['y', 'yes']:
            test_website_recording()
        else:
            print("Skipping website recording test")
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
    
    print("\n" + "=" * 50)
    print("🎬 Test suite completed!")


if __name__ == "__main__":
    main()
