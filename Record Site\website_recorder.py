import subprocess
import time
import os
import argparse
import sys
from pathlib import Path
from playwright.sync_api import sync_playwright

# Import video utilities
try:
    from video_utils import validate_video_file, optimize_video, get_video_info
except ImportError:
    print("Warning: video_utils module not found. Some video features may not work.")
    # Fallback functions
    def validate_video_file(video_path, min_duration=1.0):
        return os.path.exists(video_path) and os.path.getsize(video_path) > 0
    def optimize_video(input_path, output_path=None, quality='medium'):
        return input_path
    def get_video_info(video_path):
        return None

def extract_domain_name(url):
    """Extract the main part of the domain from a URL"""
    import re
    # Remove protocol (http://, https://, etc)
    url = re.sub(r'^https?://', '', url)
    # Remove www. if present
    url = re.sub(r'^www\.', '', url)
    # Extract the domain name (everything up to the first slash or end of string)
    domain = url.split('/')[0]
    # Extract main part (before the extension)
    main_part = domain.split('.')[0]
    return main_part

def parse_arguments():
    parser = argparse.ArgumentParser(description='Website recorder with Playwright and ffmpeg')

    # Basic configuration
    default_url = 'https://www.high-quality-roofing.com/'
    parser.add_argument('--url', type=str, default=default_url,
                        help='URL of the website to record')

    # Generate default output name based on URL
    default_output = f"{extract_domain_name(default_url)}.mp4"
    parser.add_argument('--output', type=str, default=default_output,
                        help='Output video filename or screenshot filename (for screenshot mode)')

    # Video settings
    parser.add_argument('--width', type=int, default=1280,
                        help='Viewport width')
    parser.add_argument('--height', type=int, default=720,
                        help='Viewport height')
    parser.add_argument('--framerate', type=int, default=30,
                        help='Recording framerate')
    parser.add_argument('--duration', type=int, default=0,
                        help='Maximum recording duration in seconds (0 for unlimited)')

    # Browser settings
    parser.add_argument('--browser', type=str, default='chromium', choices=['chromium', 'firefox', 'webkit'],
                        help='Browser to use')
    parser.add_argument('--headless', action='store_true',
                        help='Run browser in headless mode')

    # Recording mode
    parser.add_argument('--mode', type=str, default='scroll',
                        choices=['scroll', 'element', 'static', 'screenshot'],
                        help='Mode: scroll, element, static, or screenshot')

    # Scroll mode settings
    parser.add_argument('--scroll-steps', type=int, default=10,
                        help='Number of scroll steps (for scroll mode)')
    parser.add_argument('--scroll-delay', type=float, default=1.0,
                        help='Delay between scroll steps in seconds (for scroll mode)')
    parser.add_argument('--scroll-amount', type=int, default=300,
                        help='Scroll amount in pixels per step (for scroll mode)')

    # Element mode settings
    parser.add_argument('--selector', type=str, default='',
                        help='CSS selector for the element to focus on (for element mode)')
    parser.add_argument('--highlight', action='store_true',
                        help='Highlight the selected element (for element mode)')

    # Screenshot settings
    parser.add_argument('--full-page', action='store_true',
                        help='Capture full page screenshot (for screenshot mode)')
    parser.add_argument('--quality', type=int, default=90,
                        help='Screenshot quality 0-100 (for screenshot mode, JPEG only)')
    # Scrollbars are hidden by default, --show-scrollbars can override this
    parser.add_argument('--show-scrollbars', action='store_true',
                        help='Show scrollbars in the screenshot (they are hidden by default)')
    parser.add_argument('--scrollbar-hide-delay', type=float, default=0.5,
                        help='Delay in seconds after hiding scrollbars before taking screenshot')

    return parser.parse_args()

# Video recording functionality - using improved video_utils module

def record_website_video(page, args):
    """Record website video using Playwright's built-in video recording"""
    try:
        print(f"Starting video recording for {args.duration if args.duration > 0 else 'unlimited'} seconds...")

        # Perform the recording action based on mode
        if args.mode == 'scroll':
            success = scroll_recording(page, args)
        elif args.mode == 'element':
            success = element_recording(page, args)
        elif args.mode == 'static':
            success = static_recording(page, args)
        else:
            print(f"Unknown recording mode: {args.mode}")
            return False

        if not success:
            print("Recording action failed")
            return False

        print("Video recording completed successfully")
        return True

    except Exception as e:
        print(f"Error during video recording: {e}")
        return False

def highlight_element(page, element):
    """Add a highlight border to an element"""
    page.evaluate("""(element) => {
        const originalStyle = element.getAttribute('style') || '';
        element.setAttribute('style', originalStyle + '; border: 3px solid red; box-shadow: 0 0 10px rgba(255,0,0,0.5);');
    }""", element)

def scroll_recording(page, args):
    """Perform scrolling recording"""
    print(f"Scrolling page {args.scroll_steps} times...")
    for i in range(args.scroll_steps):
        print(f"Scroll step {i+1}/{args.scroll_steps}")
        page.mouse.wheel(0, args.scroll_amount)
        time.sleep(args.scroll_delay)
    return True

def element_recording(page, args):
    """Perform element-focused recording"""
    print(f"Waiting for element with selector: {args.selector}")
    try:
        element = page.wait_for_selector(args.selector, state="visible", timeout=10000)

        if element:
            # Scroll the element into view
            element.scroll_into_view_if_needed()

            if args.highlight:
                highlight_element(page, element)

            # Calculate recording duration
            duration = args.duration if args.duration > 0 else 10
            print(f"Recording element for {duration} seconds...")
            time.sleep(duration)
            return True
        else:
            print(f"Element with selector '{args.selector}' not found")
            return False

    except Exception as e:
        print(f"Error finding element: {e}")
        return False

def static_recording(page, args):
    """Perform static page recording"""
    # Calculate recording duration
    duration = args.duration if args.duration > 0 else 10
    print(f"Recording static page for {duration} seconds...")
    time.sleep(duration)
    return True

def take_screenshot(page, args):
    """Take a screenshot of the page"""
    # Determine output filename
    output_file = args.output
    # If the output file doesn't have an image extension, add .png
    if not output_file.lower().endswith(('.png', '.jpg', '.jpeg')):
        output_file = os.path.splitext(output_file)[0] + '.png'

    # Determine screenshot options
    screenshot_options = {
        'path': output_file,
        'full_page': args.full_page
    }

    # Add quality for JPEG images
    if output_file.lower().endswith(('.jpg', '.jpeg')):
        screenshot_options['quality'] = args.quality

    # Hide scrollbars by default unless show_scrollbars is specified
    if not args.show_scrollbars:
        print("Hiding scrollbars...")
        # Use JavaScript to hide scrollbars across browsers
        page.evaluate("""
        () => {
            // Create a style element
            const style = document.createElement('style');
            style.textContent = `
                /* Hide all scrollbars */
                ::-webkit-scrollbar {
                    display: none !important;
                    width: 0 !important;
                    height: 0 !important;
                    background: transparent !important;
                }

                /* Apply to all elements */
                *, *::before, *::after {
                    scrollbar-width: none !important;
                    -ms-overflow-style: none !important;
                }

                /* Body and HTML specific */
                html, body {
                    overflow: -moz-scrollbars-none !important;
                    scrollbar-width: none !important;
                    -ms-overflow-style: none !important;
                    overflow-x: hidden !important;
                }

                /* Target common scrollbar containers */
                div, main, section, article, aside, nav, header, footer {
                    scrollbar-width: none !important;
                    -ms-overflow-style: none !important;
                    overflow-x: hidden !important;
                }
            `;

            // Add the style to the document head
            document.head.appendChild(style);

            // Force scrollbar hiding on elements with overflow
            const allElements = document.querySelectorAll('*');
            for (const el of allElements) {
                const style = window.getComputedStyle(el);
                if (style.overflow === 'auto' || style.overflow === 'scroll' ||
                    style.overflowY === 'auto' || style.overflowY === 'scroll' ||
                    style.overflowX === 'auto' || style.overflowX === 'scroll') {
                    el.style.setProperty('scrollbar-width', 'none', 'important');
                    el.style.setProperty('-ms-overflow-style', 'none', 'important');
                    el.style.setProperty('overflow', 'auto', 'important');
                }
            }
        }
        """)

    # Add a delay after hiding scrollbars before taking the screenshot
    if not args.show_scrollbars:
        print(f"Waiting {args.scrollbar_hide_delay} seconds for scrollbars to be fully hidden...")
        time.sleep(args.scrollbar_hide_delay)

    # Now take the screenshot
    print(f"Taking {'full-page ' if args.full_page else ''}screenshot...")
    page.screenshot(**screenshot_options)

    print(f"Screenshot saved to {output_file}")
    return True

def main():
    args = parse_arguments()

    # If user didn't specify an output name, generate one based on the URL
    if args.output == f"{extract_domain_name('https://www.high-quality-roofing.com/')}.mp4":
        args.output = f"{extract_domain_name(args.url)}.mp4"

    # Video recording is now enabled with improved implementation
    print(f"Mode: {args.mode}")
    print(f"Output: {args.output}")

    try:
        # Launch browser and navigate to the page
        with sync_playwright() as p:
            print(f"Launching {args.browser} browser and navigating to {args.url}...")

            # Select browser
            if args.browser == 'chromium':
                browser_type = p.chromium
            elif args.browser == 'firefox':
                browser_type = p.firefox
            elif args.browser == 'webkit':
                browser_type = p.webkit

            browser = browser_type.launch(headless=args.headless)

            # Configure context based on mode
            context_options = {
                "viewport": {"width": args.width, "height": args.height}
            }

            # Enable video recording for video modes
            if args.mode != 'screenshot':
                # Create a temporary directory for video recording
                import tempfile
                video_dir = tempfile.mkdtemp()
                context_options["record_video_dir"] = video_dir
                context_options["record_video_size"] = {"width": args.width, "height": args.height}
                print(f"Video recording enabled, saving to: {video_dir}")

            context = browser.new_context(**context_options)
            page = context.new_page()
            page.goto(args.url)

            # Wait for page to load completely
            page.wait_for_load_state("networkidle")

            # Execute the appropriate action
            if args.mode == 'screenshot':
                success = take_screenshot(page, args)
            else:
                # Record video
                success = record_website_video(page, args)

                if success:
                    # Close the page to finalize video recording
                    page.close()
                    context.close()

                    # Find the recorded video file
                    import glob
                    video_files = glob.glob(os.path.join(video_dir, "*.webm"))
                    if video_files:
                        temp_video = video_files[0]
                        print(f"Raw video recorded: {temp_video}")

                        # Convert and optimize the video
                        final_output = args.output
                        if not final_output.lower().endswith(('.mp4', '.avi', '.mov', '.mkv')):
                            final_output = os.path.splitext(final_output)[0] + '.mp4'

                        # Convert webm to mp4 and optimize
                        optimized_video = optimize_video(temp_video, final_output, quality='medium')

                        if optimized_video and validate_video_file(optimized_video):
                            print(f"Video successfully created: {optimized_video}")
                            success = True
                        else:
                            print("Video creation failed or produced corrupted file")
                            success = False

                        # Clean up temporary files
                        try:
                            import shutil
                            shutil.rmtree(video_dir)
                        except:
                            pass
                    else:
                        print("No video file was created")
                        success = False

            # Close the browser if not already closed
            if not page.is_closed():
                browser.close()

    except Exception as e:
        print(f"An error occurred: {e}")

if __name__ == "__main__":
    main()