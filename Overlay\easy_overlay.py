"""
Easy-to-use script for creating a Loom-style circular video overlay.
This script will guide you through the process of selecting your files.
"""
import os
import sys
import numpy as np
import cv2
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip

def circular_mask(frame):
    """Apply a circular mask to a frame, making everything outside the circle transparent."""
    height, width = frame.shape[:2]
    mask = np.zeros((height, width), dtype=np.uint8)
    center = (width // 2, height // 2)
    radius = min(center)  # full circle in square
    cv2.circle(mask, center, radius, 255, -1)

    # Convert to BGRA to handle transparency
    if frame.shape[2] == 3:  # RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGRA)
    # Set alpha channel based on mask
    frame[:, :, 3] = mask
    return frame

def create_overlay(background_path, overlay_path, output_path, overlay_size=200, position=("right", "bottom")):
    """Create a circular video overlay on a background image or video."""
    print(f"\nProcessing: {os.path.basename(overlay_path)} -> {os.path.basename(background_path)}")
    
    try:
        # Load overlay video
        overlay = VideoFileClip(overlay_path)
        
        # Determine if background is image or video
        bg_ext = os.path.splitext(background_path)[1].lower()
        is_image = bg_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        # Load background
        if is_image:
            # Load static image and get video duration from overlay
            bg = ImageClip(background_path, duration=overlay.duration)
            print(f"Background is an image. Output duration will be {overlay.duration:.2f} seconds.")
        else:
            # Both are videos
            bg = VideoFileClip(background_path)
            
            # If overlay is longer than background, trim it
            if overlay.duration > bg.duration:
                print(f"Trimming overlay video to match background duration ({bg.duration:.2f} seconds).")
                overlay = overlay.subclip(0, bg.duration)
            else:
                print(f"Output duration will be {bg.duration:.2f} seconds.")
        
        # Resize overlay and apply circular mask
        print(f"Applying circular mask to overlay video...")
        overlay = overlay.resize(height=overlay_size)
        overlay = overlay.fl_image(circular_mask).set_position(position)
        
        # Combine clips
        print(f"Combining videos...")
        final = CompositeVideoClip([bg, overlay])
        
        # Write output file with appropriate settings
        print(f"Rendering output video to {output_path}...")
        final.write_videofile(
            output_path, 
            codec='libx264', 
            audio=True if not is_image else False,
            fps=24
        )
        
        print(f"\nSuccess! Output saved to: {output_path}")
        return True
        
    except Exception as e:
        print(f"\nError: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("LOOM-STYLE CIRCULAR VIDEO OVERLAY CREATOR")
    print("=" * 60)
    
    # Get background file path
    if len(sys.argv) > 1 and os.path.exists(sys.argv[1]):
        background_path = sys.argv[1]
    else:
        background_path = input("\nEnter the full path to your background image or video\n(e.g., C:\\Users\\<USER>\\Desktop\\high-quality-roofing.png): ")
        
    if not os.path.exists(background_path):
        print(f"Error: File '{background_path}' not found.")
        return
        
    # Get overlay file path
    if len(sys.argv) > 2 and os.path.exists(sys.argv[2]):
        overlay_path = sys.argv[2]
    else:
        overlay_path = input("\nEnter the full path to your overlay video\n(e.g., C:\\Users\\<USER>\\Desktop\\CJ.mp4): ")
        
    if not os.path.exists(overlay_path):
        print(f"Error: File '{overlay_path}' not found.")
        return
    
    # Get output file path
    if len(sys.argv) > 3:
        output_path = sys.argv[3]
    else:
        default_output = os.path.join(os.path.dirname(background_path), "output_with_overlay.mp4")
        output_suggestion = input(f"\nEnter the output file path (press Enter for default: {default_output}): ")
        output_path = output_suggestion if output_suggestion else default_output
    
    # Get overlay size
    try:
        size_input = input("\nEnter the overlay size in pixels (press Enter for default: 200): ")
        overlay_size = int(size_input) if size_input else 200
    except ValueError:
        overlay_size = 200
        print("Invalid size. Using default: 200 pixels.")
    
    # Get position
    position_options = {
        "1": ("left", "bottom"),
        "2": ("right", "bottom"),
        "3": ("left", "top"),
        "4": ("right", "top"),
    }
    
    print("\nSelect overlay position:")
    print("1. Bottom-left")
    print("2. Bottom-right (recommended)")
    print("3. Top-left")
    print("4. Top-right")
    
    position_input = input("Enter your choice (1-4, press Enter for default: 2): ")
    position = position_options.get(position_input, ("right", "bottom"))
    
    # Create the overlay
    success = create_overlay(background_path, overlay_path, output_path, overlay_size, position)
    
    if success:
        print("\nTip: To run this again with the same files, you can use:")
        print(f"python {os.path.basename(__file__)} \"{background_path}\" \"{overlay_path}\" \"{output_path}\"")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
    except Exception as e:
        print(f"\nUnexpected error: {str(e)}")
    finally:
        print("\nThank you for using the Loom-style overlay creator!")
