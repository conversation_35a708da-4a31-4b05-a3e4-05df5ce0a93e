#!/usr/bin/env python3
"""
Loom-Style Video Generator Configuration and Management

This script helps manage configurations, templates, and provides
easy-to-use presets for different B2B cold email scenarios.
"""

import os
import json
import argparse
from datetime import datetime
from pathlib import Path


# Predefined templates for different use cases
TEMPLATES = {
    "quick_demo": {
        "name": "Quick Demo (15s)",
        "description": "Fast 15-second demo for initial outreach",
        "settings": {
            "mode": "scroll",
            "duration": 15,
            "width": 1280,
            "height": 720,
            "avatar_size": 180,
            "position": "bottom-right",
            "audio_volume": 1.0,
            "scroll_steps": 3,
            "scroll_delay": 3.0,
            "quality": "medium"
        }
    },
    
    "detailed_walkthrough": {
        "name": "Detailed Walkthrough (30s)",
        "description": "Comprehensive 30-second walkthrough for qualified leads",
        "settings": {
            "mode": "scroll",
            "duration": 30,
            "width": 1920,
            "height": 1080,
            "avatar_size": 220,
            "position": "bottom-right",
            "audio_volume": 1.0,
            "scroll_steps": 6,
            "scroll_delay": 3.5,
            "quality": "high"
        }
    },
    
    "element_focus": {
        "name": "Element Focus",
        "description": "Focus on specific website elements",
        "settings": {
            "mode": "element",
            "duration": 20,
            "width": 1280,
            "height": 720,
            "avatar_size": 200,
            "position": "top-right",
            "audio_volume": 1.0,
            "highlight": True,
            "selector": "h1, .hero, .main-content",
            "quality": "medium"
        }
    },
    
    "static_presentation": {
        "name": "Static Presentation",
        "description": "Static view with avatar presentation",
        "settings": {
            "mode": "static",
            "duration": 25,
            "width": 1280,
            "height": 720,
            "avatar_size": 250,
            "position": "bottom-left",
            "audio_volume": 1.0,
            "quality": "medium"
        }
    },
    
    "mobile_optimized": {
        "name": "Mobile Optimized",
        "description": "Optimized for mobile viewing",
        "settings": {
            "mode": "scroll",
            "duration": 20,
            "width": 720,
            "height": 1280,
            "avatar_size": 150,
            "position": "top-right",
            "audio_volume": 1.0,
            "scroll_steps": 4,
            "scroll_delay": 3.0,
            "quality": "medium"
        }
    }
}


def create_template(template_name, output_file=None):
    """Create a template file from predefined templates"""
    if template_name not in TEMPLATES:
        print(f"❌ Template '{template_name}' not found.")
        print(f"Available templates: {', '.join(TEMPLATES.keys())}")
        return False
    
    template = TEMPLATES[template_name].copy()
    template["created_at"] = datetime.now().isoformat()
    
    if output_file is None:
        output_file = f"template_{template_name}.json"
    
    try:
        with open(output_file, 'w') as f:
            json.dump(template, f, indent=2)
        
        print(f"✅ Created template: {output_file}")
        print(f"   Name: {template['name']}")
        print(f"   Description: {template['description']}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating template: {e}")
        return False


def create_prospect_list(prospects_data, output_file="prospects.json"):
    """Create a prospects JSON file from various input formats"""
    try:
        # If prospects_data is a file, read it
        if os.path.exists(prospects_data):
            if prospects_data.endswith('.csv'):
                prospects = read_csv_prospects(prospects_data)
            elif prospects_data.endswith('.json'):
                with open(prospects_data, 'r') as f:
                    prospects = json.load(f)
            else:
                print(f"❌ Unsupported file format: {prospects_data}")
                return False
        else:
            # Assume it's JSON string
            prospects = json.loads(prospects_data)
        
        # Validate and save
        if validate_prospects(prospects):
            with open(output_file, 'w') as f:
                json.dump(prospects, f, indent=2)
            
            print(f"✅ Created prospects file: {output_file}")
            print(f"   Total prospects: {len(prospects)}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ Error creating prospects file: {e}")
        return False


def read_csv_prospects(csv_file):
    """Read prospects from CSV file"""
    import csv
    
    prospects = []
    with open(csv_file, 'r', newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Map common CSV column names
            prospect = {
                'name': row.get('name') or row.get('Name') or row.get('full_name'),
                'email': row.get('email') or row.get('Email') or row.get('email_address'),
                'website': row.get('website') or row.get('Website') or row.get('url'),
                'company': row.get('company') or row.get('Company') or row.get('organization'),
                'notes': row.get('notes') or row.get('Notes') or row.get('description', '')
            }
            
            # Only add if we have required fields
            if prospect['name'] and prospect['email'] and prospect['website']:
                prospects.append(prospect)
    
    return prospects


def validate_prospects(prospects):
    """Validate prospects data"""
    if not isinstance(prospects, list):
        print("❌ Prospects must be a list")
        return False
    
    required_fields = ['name', 'email', 'website']
    
    for i, prospect in enumerate(prospects):
        if not isinstance(prospect, dict):
            print(f"❌ Prospect {i+1} must be a dictionary")
            return False
        
        for field in required_fields:
            if field not in prospect or not prospect[field]:
                print(f"❌ Prospect {i+1} missing required field: {field}")
                return False
        
        # Validate website URL
        website = prospect['website']
        if not website.startswith(('http://', 'https://')):
            prospect['website'] = 'https://' + website
    
    print(f"✅ Validated {len(prospects)} prospects")
    return True


def list_templates():
    """List all available templates"""
    print("📋 Available Templates:")
    print("=" * 50)
    
    for key, template in TEMPLATES.items():
        print(f"\n🎬 {key}")
        print(f"   Name: {template['name']}")
        print(f"   Description: {template['description']}")
        
        settings = template['settings']
        print(f"   Duration: {settings.get('duration', 'N/A')}s")
        print(f"   Mode: {settings.get('mode', 'N/A')}")
        print(f"   Resolution: {settings.get('width', 'N/A')}x{settings.get('height', 'N/A')}")


def create_sample_prospects():
    """Create sample prospects for testing"""
    sample_prospects = [
        {
            "name": "Alex Thompson",
            "email": "<EMAIL>",
            "website": "https://example.com",
            "company": "TechCorp Solutions",
            "title": "CTO",
            "notes": "Interested in automation tools"
        },
        {
            "name": "Maria Garcia",
            "email": "<EMAIL>",
            "website": "https://news.ycombinator.com",
            "company": "Creative Design Studio",
            "title": "Creative Director",
            "notes": "Looking for design workflow improvements"
        },
        {
            "name": "David Kim",
            "email": "<EMAIL>",
            "website": "https://httpbin.org",
            "company": "E-commerce Plus",
            "title": "Marketing Manager",
            "notes": "Needs better analytics and reporting"
        }
    ]
    
    with open('prospects_sample.json', 'w') as f:
        json.dump(sample_prospects, f, indent=2)
    
    print("✅ Created sample prospects: prospects_sample.json")


def show_config_help():
    """Show configuration help and examples"""
    print("""
🎬 Loom-Style Video Generator Configuration Help
===============================================

QUICK START:
1. Create a template:
   python loom_config.py --create-template quick_demo

2. Create sample prospects:
   python loom_config.py --create-samples

3. Generate videos:
   python loom_style_generator.py --url https://prospect.com --avatar speaker.mp4 --output demo.mp4

BATCH PROCESSING:
1. Create template and prospects:
   python loom_config.py --create-template detailed_walkthrough
   python loom_config.py --create-samples

2. Run batch processing:
   python batch_loom_generator.py --prospects prospects_sample.json --avatar speaker.mp4 --template template_detailed_walkthrough.json

TEMPLATES:
- quick_demo: 15-second fast demo
- detailed_walkthrough: 30-second comprehensive demo  
- element_focus: Focus on specific website elements
- static_presentation: Static view with avatar
- mobile_optimized: Optimized for mobile viewing

PROSPECT FILE FORMAT:
{
  "name": "John Smith",
  "email": "<EMAIL>", 
  "website": "https://company.com",
  "company": "Company Inc",
  "notes": "Optional notes"
}

AUDIO OPTIONS:
- Use avatar video audio (default)
- Add external audio file (--audio voiceover.mp3)
- Adjust audio volume (--audio-volume 1.5)

AVATAR OPTIONS:
- Video file (.mp4, .avi, .mov)
- Image file (.jpg, .png) - will be converted to video
- Circular mask applied automatically
    """)


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Loom-Style Video Generator Configuration",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument('--list-templates', action='store_true', help='List available templates')
    parser.add_argument('--create-template', help='Create template file (template name)')
    parser.add_argument('--create-samples', action='store_true', help='Create sample prospects file')
    parser.add_argument('--create-prospects', help='Create prospects from CSV or JSON file')
    parser.add_argument('--output', help='Output file name')
    parser.add_argument('--help-config', action='store_true', help='Show detailed configuration help')
    
    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()
    
    if args.help_config:
        show_config_help()
        return 0
    
    if args.list_templates:
        list_templates()
        return 0
    
    if args.create_template:
        success = create_template(args.create_template, args.output)
        return 0 if success else 1
    
    if args.create_samples:
        create_sample_prospects()
        return 0
    
    if args.create_prospects:
        output_file = args.output or "prospects.json"
        success = create_prospect_list(args.create_prospects, output_file)
        return 0 if success else 1
    
    # Default: show help
    print("🎬 Loom-Style Video Generator Configuration")
    print("Use --help to see available options")
    print("Use --help-config for detailed configuration help")
    return 0


if __name__ == "__main__":
    exit(main())
