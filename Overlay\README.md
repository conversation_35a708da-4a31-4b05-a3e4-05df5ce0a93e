# Circular Video Overlay (Loom-Style)

This Python application overlays an MP4 video in a circular shape onto a static image or another video, similar to how Loom displays webcam footage in screen recordings.

## Features

- Overlay a circular video on top of an image or another video
- Customize the size of the overlay
- Position the overlay in different corners (bottom-left, bottom-right, top-left, top-right)
- Preserves audio from video sources
- Easy command-line interface

## Installation

1. Clone this repository or download the files
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

### Command Line Interface

```bash
python circular_video_overlay.py --background path/to/background.jpg --overlay path/to/overlay.mp4 --output output.mp4
```

### Arguments

- `--background`: Path to the background image or video
- `--overlay`: Path to the video to be overlaid in circular shape
- `--output`: Path to save the output video
- `--size`: Height of overlay in pixels (default: 200)
- `--position`: Position of overlay (choices: bottom-left, bottom-right, top-left, top-right, default: bottom-left)

### Example

```bash
# Overlay webcam video on a screenshot
python circular_video_overlay.py --background screenshot.jpg --overlay webcam.mp4 --output loom_style.mp4 --position bottom-right --size 180

# Overlay webcam video on another video
python circular_video_overlay.py --background screen_recording.mp4 --overlay webcam.mp4 --output presentation.mp4
```

## Programmatic Usage

You can also use the core function in your own Python scripts:

```python
from circular_video_overlay import overlay_circular_video

overlay_circular_video(
    background_path="screenshot.jpg",
    overlay_video_path="webcam.mp4",
    output_path="output.mp4",
    overlay_size=200,
    position=("left", "bottom")
)
```

## Requirements

- Python 3.6+
- MoviePy
- OpenCV (cv2)
- NumPy

## Notes

- For transparency to work correctly, ensure the output format supports alpha if needed (e.g., webm or mov)
- The circular mask is applied to each frame of the overlay video, which may impact performance for very long videos
- If the overlay video is longer than the background video, it will be trimmed to match the background duration
