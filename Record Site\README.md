# Website Screen Recorder - Enhanced Edition

A powerful Python tool for recording websites using Playwright and ffmpeg with advanced video processing capabilities. This tool allows you to record website scrolling, specific elements, or static pages with robust video validation and optimization.

## 🎬 New Features

- **Improved Video Recording**: Uses <PERSON><PERSON>'s built-in video recording for better reliability
- **Video Validation**: Comprehensive video file validation to prevent corrupted outputs
- **Video Optimization**: Multiple quality presets and automatic optimization
- **Error Recovery**: Automatic video repair and corruption detection
- **Better Compatibility**: Enhanced encoding settings for maximum compatibility

## Prerequisites

- Python 3.7+
- Playwright
- ffmpeg (included in the project or available in your PATH)

## Installation

1. Install Python dependencies:
   ```bash
   pip install playwright
   playwright install
   ```

2. FFmpeg is included in the `ffmpeg/` directory, but you can also install it system-wide:
   - Windows: Download from [ffmpeg.org](https://ffmpeg.org/download.html) and add to PATH
   - macOS: `brew install ffmpeg`
   - Ubuntu: `sudo apt install ffmpeg`

## Usage

The script supports multiple recording modes and customization options:

```bash
python website_recorder.py --url https://example.com --mode scroll
```

### Recording Modes

- `--mode scroll`: Record scrolling through the page (default)
- `--mode element`: Record a specific element (requires `--selector`)
- `--mode static`: Record a static page for a specified duration
- `--mode screenshot`: Take a screenshot instead of recording video

### Basic Arguments

- `--url`: Website URL to record (default: https://www.high-quality-roofing.com/)
- `--output`: Output filename (auto-generated based on URL if not specified)
- `--duration`: Recording duration in seconds (0 for unlimited, default varies by mode)

### Video Quality Options

- `--width`: Viewport width (default: 1280)
- `--height`: Viewport height (default: 720)
- `--framerate`: Recording framerate (default: 30)

### Browser Options

- `--browser`: Browser to use (chromium, firefox, webkit - default: chromium)
- `--headless`: Run browser in headless mode

### Scroll Mode Options

- `--scroll-steps`: Number of scroll steps (default: 10)
- `--scroll-delay`: Delay between scroll steps in seconds (default: 1.0)
- `--scroll-amount`: Scroll amount in pixels per step (default: 300)

### Element Mode Options

- `--selector`: CSS selector for the element to focus on
- `--highlight`: Highlight the selected element with a red border

### Screenshot Mode Options

- `--full-page`: Capture full page screenshot
- `--quality`: Screenshot quality 0-100 (for JPEG only, default: 90)
- `--show-scrollbars`: Show scrollbars in screenshot (hidden by default)
- `--scrollbar-hide-delay`: Delay after hiding scrollbars (default: 0.5s)

## 🎬 Examples

### Video Recording Examples

#### Record a website with scrolling (default mode)
```bash
python website_recorder.py --url https://news.ycombinator.com
```

#### Record a specific element with highlighting
```bash
python website_recorder.py --url https://example.com --mode element --selector ".main-content" --highlight --duration 15
```

#### Record in high resolution with custom settings
```bash
python website_recorder.py --url https://example.com --width 1920 --height 1080 --browser firefox --output hd_recording.mp4 --duration 20
```

#### Record a static page for 30 seconds
```bash
python website_recorder.py --url https://example.com --mode static --duration 30
```

### Screenshot Examples

#### Take a full-page screenshot
```bash
python website_recorder.py --url https://example.com --mode screenshot --full-page
```

#### Take a viewport screenshot with custom quality
```bash
python website_recorder.py --url https://example.com --mode screenshot --quality 95 --output screenshot.jpg
```

## 🔧 Video Utilities

The project includes a comprehensive `video_utils.py` module with additional video processing capabilities:

### Video Validation
```python
from video_utils import validate_video_file
is_valid = validate_video_file("video.mp4", min_duration=5.0)
```

### Video Optimization
```python
from video_utils import optimize_video
optimized_path = optimize_video("input.mp4", "output.mp4", quality="high")
```

### Video Information
```python
from video_utils import get_video_info
info = get_video_info("video.mp4")
```

### Video Repair
```python
from video_utils import repair_video
repaired_path = repair_video("corrupted.mp4", "repaired.mp4")
```

## 🧪 Testing

Run the test suite to verify everything is working:

```bash
python test_video_recording.py
```

This will test:
- Video validation functionality
- Video information extraction
- Video optimization
- Website recording (optional)

## 🔧 Troubleshooting

### Video Issues
- **Corrupted videos**: The new implementation includes automatic video validation and repair
- **Large file sizes**: Use the optimization features with different quality presets
- **Compatibility issues**: Videos are now encoded with maximum compatibility settings

### FFmpeg Issues
- FFmpeg is included in the `ffmpeg/` directory and should work out of the box
- If you encounter issues, ensure FFmpeg has proper permissions
- For system-wide FFmpeg, ensure it's in your PATH

### Browser Issues
- **Element not found**: Use browser dev tools to verify CSS selectors
- **Page loading issues**: Increase wait times or check network connectivity
- **Headless mode problems**: Try running without `--headless` for debugging

### Performance Tips
- Use `--headless` mode for faster recording
- Lower resolution (`--width 1280 --height 720`) for smaller files
- Use `quality="low"` for faster processing
- Close other applications to free up system resources

## 🆕 What's Fixed

The enhanced version addresses the previous corrupted video issues by:

1. **Using Playwright's native video recording** instead of screen capture
2. **Implementing comprehensive video validation** to catch corruption early
3. **Adding automatic video optimization** with proven encoding settings
4. **Including video repair capabilities** for damaged files
5. **Providing multiple quality presets** for different use cases
6. **Adding proper error handling and cleanup** throughout the process

## 📁 Project Structure

```
Record Site/
├── website_recorder.py      # Main recording script
├── video_utils.py          # Video processing utilities
├── test_video_recording.py # Test suite
├── requirements.txt        # Python dependencies
├── README.md              # This file
└── ffmpeg/                # Bundled FFmpeg binaries
    └── bin/
        ├── ffmpeg.exe
        └── ffprobe.exe
```