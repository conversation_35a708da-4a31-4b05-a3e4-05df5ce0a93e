"""
Demo script for the circular video overlay application.
This script creates sample files and demonstrates how to use the overlay function.
"""
import os
import numpy as np
import cv2
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip, ColorClip
from circular_video_overlay import overlay_circular_video

def create_sample_files():
    """Create sample background and overlay files for demonstration"""
    # Create a sample background image (blue gradient)
    height, width = 720, 1280
    background = np.zeros((height, width, 3), dtype=np.uint8)
    for i in range(height):
        background[i, :, 0] = int(255 * i / height)  # Blue gradient
    
    # Add some text to the background
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(background, 'Sample Background', (width//4, height//2), 
                font, 2, (255, 255, 255), 3, cv2.LINE_AA)
    
    # Save the background image
    bg_path = 'sample_background.jpg'
    cv2.imwrite(bg_path, background)
    print(f"Created sample background image: {bg_path}")
    
    # Create a sample overlay video (moving red circle)
    duration = 5  # seconds
    fps = 24
    
    def make_frame(t):
        # Create a frame with a moving red circle
        frame = np.zeros((300, 300, 3), dtype=np.uint8)
        # Calculate position based on time
        x = int(150 + 100 * np.cos(t * 2 * np.pi / duration))
        y = int(150 + 100 * np.sin(t * 2 * np.pi / duration))
        # Draw the circle
        cv2.circle(frame, (x, y), 30, (0, 0, 255), -1)
        return frame
    
    # Create a clip from the frames
    clip = VideoClip(make_frame, duration=duration)
    clip = clip.set_fps(fps)
    
    # Save the overlay video
    overlay_path = 'sample_overlay.mp4'
    clip.write_videofile(overlay_path, codec='libx264', fps=fps)
    print(f"Created sample overlay video: {overlay_path}")
    
    return bg_path, overlay_path

def run_demo():
    """Run a demonstration of the circular video overlay"""
    # Create sample files
    bg_path, overlay_path = create_sample_files()
    
    # Define output path
    output_path = 'demo_output.mp4'
    
    # Run the overlay function
    print("\nRunning circular video overlay...")
    overlay_circular_video(
        background_path=bg_path,
        overlay_video_path=overlay_path,
        output_path=output_path,
        overlay_size=150,
        position=("right", "bottom")
    )
    
    print(f"\nDemo completed! Output saved to: {output_path}")
    print("Try playing the output video to see the circular overlay effect.")

if __name__ == "__main__":
    run_demo()
