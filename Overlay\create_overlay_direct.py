"""
Direct script to create a circular video overlay with the specific files.
"""
import os
import numpy as np
import cv2
from moviepy.editor import VideoFileClip, ImageClip, CompositeVideoClip

def circular_mask(frame):
    """Apply a circular mask to a frame, making everything outside the circle transparent."""
    height, width = frame.shape[:2]
    mask = np.zeros((height, width), dtype=np.uint8)
    center = (width // 2, height // 2)
    radius = min(center)  # full circle in square
    cv2.circle(mask, center, radius, 255, -1)

    # Convert to BGRA to handle transparency
    if frame.shape[2] == 3:  # RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGRA)
    # Set alpha channel based on mask
    frame[:, :, 3] = mask
    return frame

# File paths - these are the exact files in your directory
background_path = "high-quality-roofing.png"
overlay_path = "CJ.mp4"
output_path = "loom_style_output.mp4"

print(f"Starting to process overlay of '{overlay_path}' onto '{background_path}'...")

try:
    # Load overlay video
    overlay = VideoFileClip(overlay_path)
    print(f"Loaded overlay video: {overlay_path}")
    
    # Load background image
    bg = ImageClip(background_path, duration=overlay.duration)
    print(f"Loaded background image: {background_path}")
    print(f"Output duration will be {overlay.duration:.2f} seconds")
    
    # Resize overlay and apply circular mask
    overlay_size = 200  # Height in pixels
    position = ("right", "bottom")  # Position of overlay
    
    print("Applying circular mask to overlay video...")
    overlay = overlay.resize(height=overlay_size)
    overlay = overlay.fl_image(circular_mask).set_position(position)
    
    # Combine clips
    print("Combining video and image...")
    final = CompositeVideoClip([bg, overlay])
    
    # Write output file with appropriate settings
    print(f"Rendering output video to {output_path}...")
    final.write_videofile(
        output_path, 
        codec='libx264', 
        audio=False,
        fps=24
    )
    
    print(f"\nSuccess! Output saved to: {output_path}")
    
except Exception as e:
    print(f"Error processing files: {str(e)}")
