"""
Simple script to create a circular video overlay using OpenCV directly.
This avoids the dependency on MoviePy which seems to be causing issues.
"""
import cv2
import numpy as np
import os

def create_circular_mask(h, w, center=None, radius=None):
    """Create a circular mask for a frame"""
    if center is None:  # use the middle of the image
        center = (int(w/2), int(h/2))
    if radius is None:  # use the smallest distance between the center and image walls
        radius = min(center[0], center[1], w-center[0], h-center[1])

    Y, X = np.ogrid[:h, :w]
    dist_from_center = np.sqrt((X - center[0])**2 + (Y-center[1])**2)

    mask = dist_from_center <= radius
    return mask

def process_video():
    # File paths
    background_path = "high-quality-roofing.png"
    overlay_path = "CJ.mp4"
    output_path = "loom_style_output.mp4"
    
    print(f"Starting to process overlay of '{overlay_path}' onto '{background_path}'...")
    
    # Check if files exist
    if not os.path.exists(background_path):
        print(f"Error: Background file '{background_path}' not found")
        return
    if not os.path.exists(overlay_path):
        print(f"Error: Overlay file '{overlay_path}' not found")
        return
    
    # Load background image
    background = cv2.imread(background_path)
    if background is None:
        print(f"Error: Could not load background image '{background_path}'")
        return
    
    # Get background dimensions
    bg_height, bg_width = background.shape[:2]
    
    # Open the overlay video
    cap = cv2.VideoCapture(overlay_path)
    if not cap.isOpened():
        print(f"Error: Could not open overlay video '{overlay_path}'")
        return
    
    # Get overlay video properties
    overlay_width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    overlay_height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Calculate new dimensions for overlay (circular)
    overlay_new_height = 200  # Height in pixels
    overlay_new_width = int(overlay_width * (overlay_new_height / overlay_height))
    
    # Create video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (bg_width, bg_height))
    
    # Calculate position for bottom-left corner
    x_position = 20  # 20 pixels from left edge
    y_position = bg_height - overlay_new_height - 20  # 20 pixels from bottom edge
    
    print(f"Processing {frame_count} frames...")
    frame_number = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
            
        # Resize overlay frame
        overlay_frame = cv2.resize(frame, (overlay_new_width, overlay_new_height))
        
        # Create a circular mask
        mask = create_circular_mask(overlay_new_height, overlay_new_width)
        mask = mask.astype(np.uint8) * 255
        
        # Create a copy of the background for this frame
        result = background.copy()
        
        # Create ROI on background
        roi = result[y_position:y_position+overlay_new_height, 
                    x_position:x_position+overlay_new_width]
        
        # Create masked overlay
        masked_overlay = cv2.bitwise_and(overlay_frame, overlay_frame, mask=mask)
        
        # Create inverse mask for background
        mask_inv = cv2.bitwise_not(mask)
        
        # Black out the area of overlay in ROI
        roi_bg = cv2.bitwise_and(roi, roi, mask=mask_inv)
        
        # Add the masked overlay to the blacked out area
        dst = cv2.add(roi_bg, masked_overlay)
        
        # Put the combined overlay back into the background
        result[y_position:y_position+overlay_new_height, 
              x_position:x_position+overlay_new_width] = dst
        
        # Write the frame
        out.write(result)
        
        frame_number += 1
        if frame_number % 100 == 0:
            print(f"Processed {frame_number}/{frame_count} frames")
    
    # Release resources
    cap.release()
    out.release()
    
    print(f"\nSuccess! Output saved to: {output_path}")

if __name__ == "__main__":
    try:
        process_video()
    except Exception as e:
        print(f"Error: {str(e)}")
