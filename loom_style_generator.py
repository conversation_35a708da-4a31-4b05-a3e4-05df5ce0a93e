#!/usr/bin/env python3
"""
Loom-Style Video Generator for B2B Cold Emails

This script creates personalized Loom-style videos by:
1. Recording a prospect's website
2. Adding a circular avatar overlay (video or image)
3. Managing audio from multiple sources
4. Optimizing for cold email campaigns

Usage:
    python loom_style_generator.py --url https://prospect-website.com --avatar avatar.mp4 --output prospect_video.mp4
"""

import os
import sys
import argparse
import tempfile
import shutil
from pathlib import Path
import json

# Add Record Site to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'Record Site'))

try:
    from video_utils import validate_video_file, optimize_video, get_video_info
    from website_recorder import main as record_website
except ImportError as e:
    print(f"Error importing modules: {e}")
    print("Make sure you're running this from the project root directory")
    sys.exit(1)

try:
    import numpy as np
    import cv2
    from moviepy.editor import (
        VideoFileClip, ImageClip, CompositeVideoClip,
        AudioFileClip, concatenate_audioclips, concatenate_videoclips
    )
except ImportError as e:
    print(f"Error: Missing required dependencies: {e}")
    print("Please install: pip install moviepy opencv-python numpy")
    sys.exit(1)


def circular_mask(frame):
    """Apply a circular mask to a frame, making everything outside the circle transparent."""
    height, width = frame.shape[:2]

    # Create circular mask
    mask = np.zeros((height, width), dtype=np.uint8)
    center = (width // 2, height // 2)
    radius = min(center)  # full circle in square
    cv2.circle(mask, center, radius, 255, -1)

    # Ensure frame is RGB (3 channels)
    if len(frame.shape) == 3 and frame.shape[2] == 4:
        # Convert RGBA to RGB
        frame = frame[:, :, :3]
    elif len(frame.shape) == 2:
        # Convert grayscale to RGB
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2RGB)

    # Create RGBA frame
    if frame.shape[2] == 3:
        # Add alpha channel
        alpha_channel = mask
        frame_rgba = np.dstack((frame, alpha_channel))
        return frame_rgba

    return frame


def create_avatar_from_image(image_path, duration, output_path):
    """Create a video from a static image for use as avatar"""
    try:
        print(f"Creating avatar video from image: {image_path}")

        # Load image and create video clip
        img_clip = ImageClip(image_path, duration=duration)

        # Write to temporary video file
        img_clip.write_videofile(
            output_path,
            codec='libx264',
            fps=24,
            audio=False,
            verbose=False,
            logger=None
        )

        print(f"Avatar video created: {output_path}")
        return True

    except Exception as e:
        print(f"Error creating avatar from image: {e}")
        return False


def record_website_background(url, duration, output_path, mode='scroll', **kwargs):
    """Record a website to use as background"""
    try:
        print(f"Recording website: {url}")

        # Import and use the website recorder
        import subprocess

        # Build command for website recorder
        cmd = [
            sys.executable,
            os.path.join('Record Site', 'website_recorder.py'),
            '--url', url,
            '--mode', mode,
            '--output', output_path,
            '--headless'
        ]

        # Add duration if specified
        if duration > 0:
            cmd.extend(['--duration', str(duration)])

        # Add additional arguments
        for key, value in kwargs.items():
            if value is not None:
                if isinstance(value, bool):
                    if value:  # Only add flag if True
                        cmd.append(f'--{key.replace("_", "-")}')
                else:
                    cmd.extend([f'--{key.replace("_", "-")}', str(value)])

        print(f"Running: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        if result.returncode == 0:
            print(f"Website recorded successfully: {output_path}")
            return True
        else:
            print(f"Website recording failed: {result.stderr}")
            return False

    except Exception as e:
        print(f"Error recording website: {e}")
        return False


def create_loom_style_video(background_path, avatar_path, output_path,
                          avatar_size=200, position=("right", "bottom"),
                          audio_path=None, audio_volume=1.0):
    """Create a Loom-style video with circular avatar overlay"""
    try:
        print(f"\n🎬 Creating Loom-style video...")
        print(f"Background: {os.path.basename(background_path)}")
        print(f"Avatar: {os.path.basename(avatar_path)}")

        # Load background video
        background = VideoFileClip(background_path)
        print(f"Background duration: {background.duration:.2f} seconds")

        # Load avatar (video or image)
        avatar_ext = os.path.splitext(avatar_path)[1].lower()
        if avatar_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
            # Create video from image
            temp_avatar = tempfile.mktemp(suffix='.mp4')
            if create_avatar_from_image(avatar_path, background.duration, temp_avatar):
                avatar = VideoFileClip(temp_avatar)
            else:
                raise Exception("Failed to create avatar video from image")
        else:
            # Load video avatar
            avatar = VideoFileClip(avatar_path)

            # Trim avatar to match background duration if needed
            if avatar.duration > background.duration:
                print(f"Trimming avatar to match background duration")
                avatar = avatar.subclip(0, background.duration)
            elif avatar.duration < background.duration:
                print(f"Looping avatar to match background duration")
                # Loop the avatar to match background duration
                loops_needed = int(background.duration / avatar.duration) + 1
                avatar = concatenate_videoclips([avatar] * loops_needed).subclip(0, background.duration)

        # Apply circular mask and resize avatar
        print(f"Applying circular mask to avatar...")
        avatar = avatar.resize(height=avatar_size)
        avatar = avatar.fl_image(circular_mask).set_position(position)

        # Combine background and avatar
        print(f"Compositing video...")
        final_video = CompositeVideoClip([background, avatar])

        # Handle audio
        final_audio = None

        # Priority: external audio > avatar audio > background audio
        if audio_path and os.path.exists(audio_path):
            print(f"Using external audio: {os.path.basename(audio_path)}")
            external_audio = AudioFileClip(audio_path)

            # Trim or loop audio to match video duration
            if external_audio.duration > final_video.duration:
                external_audio = external_audio.subclip(0, final_video.duration)
            elif external_audio.duration < final_video.duration:
                loops_needed = int(final_video.duration / external_audio.duration) + 1
                external_audio = concatenate_audioclips([external_audio] * loops_needed).subclip(0, final_video.duration)

            # Apply volume adjustment
            if audio_volume != 1.0:
                external_audio = external_audio.volumex(audio_volume)

            final_audio = external_audio

        elif avatar.audio is not None:
            print("Using audio from avatar video")
            final_audio = avatar.audio
            if audio_volume != 1.0:
                final_audio = final_audio.volumex(audio_volume)

        elif background.audio is not None:
            print("Using audio from background video")
            final_audio = background.audio
            if audio_volume != 1.0:
                final_audio = final_audio.volumex(audio_volume)
        else:
            print("No audio sources available")

        # Set final audio
        if final_audio:
            final_video = final_video.set_audio(final_audio)

        # Write output video
        print(f"Rendering final video to: {output_path}")
        final_video.write_videofile(
            output_path,
            codec='libx264',
            audio_codec='aac' if final_audio else None,
            fps=24,
            preset='medium',
            verbose=False,
            logger=None
        )

        # Clean up temporary files
        if avatar_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'] and 'temp_avatar' in locals():
            try:
                os.remove(temp_avatar)
            except:
                pass

        # Close clips to free memory
        background.close()
        avatar.close()
        if final_audio:
            final_audio.close()
        final_video.close()

        print(f"✅ Loom-style video created successfully: {output_path}")
        return True

    except Exception as e:
        print(f"❌ Error creating Loom-style video: {e}")
        return False


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Generate Loom-style videos for B2B cold emails",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage with video avatar
  python loom_style_generator.py --url https://prospect.com --avatar speaker.mp4 --output prospect_demo.mp4

  # Using image avatar with external audio
  python loom_style_generator.py --url https://prospect.com --avatar headshot.jpg --audio voiceover.mp3 --output demo.mp4

  # Custom recording settings
  python loom_style_generator.py --url https://prospect.com --avatar speaker.mp4 --duration 30 --mode scroll --avatar-size 250 --output demo.mp4
        """
    )

    # Required arguments
    parser.add_argument('--url', required=True, help='Website URL to record')
    parser.add_argument('--avatar', required=True, help='Avatar video or image file')
    parser.add_argument('--output', required=True, help='Output video file')

    # Optional arguments
    parser.add_argument('--audio', help='External audio file (MP3, WAV, etc.)')
    parser.add_argument('--duration', type=int, default=0, help='Recording duration in seconds (0 for mode default)')
    parser.add_argument('--mode', choices=['scroll', 'static', 'element'], default='scroll', help='Website recording mode')
    parser.add_argument('--avatar-size', type=int, default=200, help='Avatar size in pixels')
    parser.add_argument('--position', choices=['bottom-left', 'bottom-right', 'top-left', 'top-right'],
                        default='bottom-right', help='Avatar position')
    parser.add_argument('--audio-volume', type=float, default=1.0, help='Audio volume multiplier')
    parser.add_argument('--width', type=int, default=1280, help='Video width')
    parser.add_argument('--height', type=int, default=720, help='Video height')
    parser.add_argument('--quality', choices=['high', 'medium', 'low'], default='medium', help='Output quality')

    # Website recording specific options
    parser.add_argument('--scroll-steps', type=int, default=5, help='Number of scroll steps (scroll mode)')
    parser.add_argument('--scroll-delay', type=float, default=2.0, help='Delay between scrolls (scroll mode)')
    parser.add_argument('--selector', help='CSS selector for element mode')
    parser.add_argument('--highlight', action='store_true', help='Highlight selected element')

    return parser.parse_args()


def main():
    """Main function"""
    args = parse_arguments()

    print("🎬 Loom-Style Video Generator for B2B Cold Emails")
    print("=" * 60)

    # Validate inputs
    if not os.path.exists(args.avatar):
        print(f"❌ Error: Avatar file not found: {args.avatar}")
        return 1

    if args.audio and not os.path.exists(args.audio):
        print(f"❌ Error: Audio file not found: {args.audio}")
        return 1

    # Convert position to tuple format
    position_map = {
        'bottom-left': ('left', 'bottom'),
        'bottom-right': ('right', 'bottom'),
        'top-left': ('left', 'top'),
        'top-right': ('right', 'top')
    }
    position = position_map[args.position]

    # Create temporary directory for intermediate files
    temp_dir = tempfile.mkdtemp(prefix='loom_generator_')
    background_video = os.path.join(temp_dir, 'background.mp4')

    try:
        # Step 1: Record website
        print(f"\n📹 Step 1: Recording website background")
        recording_kwargs = {
            'width': args.width,
            'height': args.height,
            'scroll_steps': args.scroll_steps,
            'scroll_delay': args.scroll_delay,
            'selector': args.selector,
            'highlight': args.highlight
        }

        success = record_website_background(
            args.url, args.duration, background_video,
            args.mode, **recording_kwargs
        )

        if not success:
            print("❌ Failed to record website")
            return 1

        # Step 2: Create Loom-style video
        print(f"\n🎭 Step 2: Creating Loom-style composition")
        success = create_loom_style_video(
            background_video, args.avatar, args.output,
            avatar_size=args.avatar_size, position=position,
            audio_path=args.audio, audio_volume=args.audio_volume
        )

        if not success:
            print("❌ Failed to create Loom-style video")
            return 1

        # Step 3: Optimize final video
        print(f"\n⚡ Step 3: Optimizing final video")
        temp_output = args.output + '.temp'
        optimized = optimize_video(args.output, temp_output, quality=args.quality)

        if optimized and validate_video_file(optimized):
            # Replace original with optimized version
            shutil.move(optimized, args.output)
            print(f"✅ Video optimized successfully")
        else:
            print("⚠️ Optimization failed, keeping original")

        # Step 4: Final validation
        print(f"\n🔍 Step 4: Final validation")
        if validate_video_file(args.output):
            print(f"✅ Final video is valid and ready for use!")

            # Show video info
            info = get_video_info(args.output)
            if info:
                format_info = info.get('format', {})
                duration = float(format_info.get('duration', 0))
                size_mb = int(format_info.get('size', 0)) / (1024 * 1024)
                print(f"📊 Video info: {duration:.1f}s, {size_mb:.2f}MB")
        else:
            print("❌ Final video validation failed")
            return 1

        print(f"\n🎉 Success! Your B2B cold email video is ready: {args.output}")
        return 0

    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1
    finally:
        # Clean up temporary directory
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


if __name__ == "__main__":
    sys.exit(main())
